# 🏗️ **AGENT 2: STRUCTURE OPTIMIZATION AGENT - CHI TIẾT TASKS**

## 🎯 **TỔNG QUAN AGENT 2**

**<PERSON>ai trò**: <PERSON><PERSON><PERSON> tr<PERSON><PERSON> sư kế hoạch chuyên thiết kế optimal project structure
**Vị trí trong pipeline**: Agent thứ 2, nhận domain analysis từ Agent 1
**<PERSON><PERSON><PERSON> tiêu chính**: Transform domain analysis thành structured milestone và task framework

---

## 📋 **BREAKDOWN CHI TIẾT TASKS**

### **Task 2.3: Implement Structure Optimization Agent**
**<PERSON><PERSON><PERSON> tiêu**: Tạo agent thiết kế optimal project structure với 5 milestones và 25 tasks

#### **Subtask 2.3.1: Create Agent Class Structure**
**📥 INPUT:** BaseIgnitionAgent, domain analysis structure, optimization requirements

**🔧 THỰC HIỆN:**
```python
# agents/structure_optimizer.py
from multi_agent.base_agent import BaseIgnitionAgent
from multi_agent.utils.state_management import PlanGenerationState
import json
import math
from datetime import datetime, timedelta

class StructureOptimizationAgent(BaseIgnitionAgent):
    def __init__(self):
        super().__init__(
            agent_name="structure_optimizer",
            config={
                "primary_model": "gemini-2.0-flash-exp",
                "fallback_model": "gemini-1.5-pro",
                "temperature": 0.2,
                "max_tokens": 2000,
                "timeout": 45
            }
        )
        self.milestone_templates = self._load_milestone_templates()
        self.optimization_weights = {
            "time_efficiency": 0.3,
            "resource_balance": 0.25,
            "risk_mitigation": 0.2,
            "dependency_optimization": 0.25
        }
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Main structure optimization logic"""
        # Implementation sẽ được detail trong subtasks khác
        pass
```

**📤 OUTPUT:** StructureOptimizationAgent class với configuration và templates
**⏱️ Duration:** 4 giờ

---

#### **Subtask 2.3.2: Load Domain-Specific Templates**
**📥 INPUT:** Domain classification, project complexity, industry best practices

**🔧 THỰC HIỆN:**
```python
def _load_milestone_templates(self) -> dict:
    """Load milestone templates for different domains"""
    return {
        "mobile_app_development": {
            "standard_flow": [
                {
                    "name": "Research and Planning",
                    "description": "Market research, requirements analysis, technical planning",
                    "typical_duration_weeks": 2,
                    "complexity_weight": 0.7,
                    "critical_path": True,
                    "typical_tasks": [
                        "Market research and competitor analysis",
                        "User persona development and user journey mapping", 
                        "Technical architecture design and technology stack selection",
                        "UI/UX wireframing and design system creation",
                        "Project setup and development environment configuration"
                    ]
                },
                {
                    "name": "Core Development Setup",
                    "description": "Foundation development, basic app structure, core features",
                    "typical_duration_weeks": 3,
                    "complexity_weight": 1.0,
                    "critical_path": True,
                    "typical_tasks": [
                        "Mobile app project initialization and basic structure setup",
                        "User authentication system implementation",
                        "Database design and backend API development",
                        "Core navigation and app flow implementation",
                        "Basic UI components and design system implementation"
                    ]
                },
                {
                    "name": "Feature Development",
                    "description": "Main features implementation, business logic development",
                    "typical_duration_weeks": 4,
                    "complexity_weight": 1.2,
                    "critical_path": True,
                    "typical_tasks": [
                        "Product catalog and search functionality implementation",
                        "Shopping cart and checkout process development",
                        "Payment gateway integration and testing",
                        "User profile and account management features",
                        "Push notifications and communication features"
                    ]
                },
                {
                    "name": "Testing and Refinement",
                    "description": "Quality assurance, performance optimization, bug fixes",
                    "typical_duration_weeks": 2,
                    "complexity_weight": 0.8,
                    "critical_path": True,
                    "typical_tasks": [
                        "Comprehensive testing suite development and execution",
                        "Performance optimization and memory management",
                        "Security testing and vulnerability assessment",
                        "User acceptance testing and feedback integration",
                        "Bug fixes and stability improvements"
                    ]
                },
                {
                    "name": "Deployment and Launch",
                    "description": "App store submission, production deployment, launch preparation",
                    "typical_duration_weeks": 1,
                    "complexity_weight": 0.6,
                    "critical_path": True,
                    "typical_tasks": [
                        "App store optimization and submission preparation",
                        "Production environment setup and deployment",
                        "Launch marketing materials and documentation",
                        "Monitoring and analytics implementation",
                        "Post-launch support and maintenance planning"
                    ]
                }
            ]
        },
        "web_development": {
            "standard_flow": [
                {
                    "name": "Planning and Design",
                    "description": "Requirements gathering, design, architecture planning",
                    "typical_duration_weeks": 2,
                    "complexity_weight": 0.7,
                    "critical_path": True
                },
                {
                    "name": "Frontend Development", 
                    "description": "User interface development, responsive design",
                    "typical_duration_weeks": 3,
                    "complexity_weight": 1.0,
                    "critical_path": True
                },
                {
                    "name": "Backend Development",
                    "description": "Server-side logic, database, API development",
                    "typical_duration_weeks": 4,
                    "complexity_weight": 1.1,
                    "critical_path": True
                },
                {
                    "name": "Integration and Testing",
                    "description": "System integration, testing, optimization",
                    "typical_duration_weeks": 2,
                    "complexity_weight": 0.9,
                    "critical_path": True
                },
                {
                    "name": "Deployment and Launch",
                    "description": "Production deployment, launch, monitoring",
                    "typical_duration_weeks": 1,
                    "complexity_weight": 0.6,
                    "critical_path": True
                }
            ]
        }
    }

def _select_optimal_template(self, domain_analysis: dict) -> dict:
    """Select best template based on domain analysis"""
    primary_domain = domain_analysis["primary_domain"]
    complexity_level = domain_analysis["complexity_level"]
    
    # Get base template
    base_template = self.milestone_templates.get(primary_domain, 
                                                self.milestone_templates["mobile_app_development"])
    
    # Adjust based on complexity
    complexity_multipliers = {
        "beginner": 0.8,
        "intermediate": 1.0,
        "advanced": 1.3,
        "expert": 1.6
    }
    
    multiplier = complexity_multipliers.get(complexity_level, 1.0)
    
    # Apply complexity adjustments
    adjusted_template = []
    for milestone in base_template["standard_flow"]:
        adjusted_milestone = milestone.copy()
        adjusted_milestone["typical_duration_weeks"] = math.ceil(
            milestone["typical_duration_weeks"] * multiplier
        )
        adjusted_milestone["complexity_weight"] *= multiplier
        adjusted_template.append(adjusted_milestone)
    
    return {"standard_flow": adjusted_template}
```

**📤 OUTPUT:** Domain-specific milestone templates với complexity adjustments
**⏱️ Duration:** 6 giờ

---

#### **Subtask 2.3.3: Implement Milestone Generation Logic**
**📥 INPUT:** Selected template, domain requirements, time constraints

**🔧 THỰC HIỆN:**
```python
def _generate_milestones(self, domain_analysis: dict, template: dict) -> list:
    """Generate optimized milestones based on template and requirements"""
    
    milestones = []
    requirements = domain_analysis["extracted_requirements"]
    constraints = domain_analysis["constraints"]
    
    for i, milestone_template in enumerate(template["standard_flow"]):
        milestone = {
            "milestone_id": f"M{i+1}",
            "name": milestone_template["name"],
            "description": milestone_template["description"],
            "position": i + 1,
            "estimated_duration": f"{milestone_template['typical_duration_weeks']}_weeks",
            "dependencies": self._calculate_dependencies(i, template["standard_flow"]),
            "critical_path": milestone_template["critical_path"],
            "task_count": 5,  # Standard 5 tasks per milestone
            "complexity_weight": milestone_template["complexity_weight"],
            "success_criteria": self._generate_success_criteria(milestone_template, requirements)
        }
        
        # Customize based on specific requirements
        milestone = self._customize_milestone_for_requirements(milestone, requirements)
        
        milestones.append(milestone)
    
    return milestones

def _calculate_dependencies(self, milestone_index: int, template_flow: list) -> list:
    """Calculate milestone dependencies"""
    if milestone_index == 0:
        return []
    elif milestone_index == 1:
        return ["M1"]
    elif milestone_index <= 2:
        return [f"M{milestone_index}"]
    else:
        # Later milestones may depend on multiple previous ones
        return [f"M{milestone_index}"]

def _generate_success_criteria(self, milestone_template: dict, requirements: dict) -> str:
    """Generate success criteria for milestone"""
    base_criteria = {
        "Research and Planning": "Requirements documented, architecture designed, team aligned",
        "Core Development Setup": "Basic functionality working, foundation established",
        "Feature Development": "Main features implemented and tested",
        "Testing and Refinement": "Quality standards met, performance optimized",
        "Deployment and Launch": "Successfully deployed, monitoring active"
    }
    
    return base_criteria.get(milestone_template["name"], "Milestone objectives achieved")

def _customize_milestone_for_requirements(self, milestone: dict, requirements: dict) -> dict:
    """Customize milestone based on specific requirements"""
    
    # Add e-commerce specific customizations
    if "payment_processing" in requirements.get("functional", []):
        if "Feature Development" in milestone["name"]:
            milestone["name"] = "Feature Development and Payment Integration"
            milestone["description"] += " with secure payment processing"
    
    # Add security customizations
    if "security" in requirements.get("non_functional", []):
        if "Testing" in milestone["name"]:
            milestone["description"] += " including security audits"
    
    return milestone
```

**📤 OUTPUT:** Milestone generation logic với customization capabilities
**⏱️ Duration:** 8 giờ

---

#### **Subtask 2.3.4: Implement Task Distribution Algorithm**
**📥 INPUT:** Generated milestones, domain requirements, task templates

**🔧 THỰC HIỆN:**
```python
def _generate_tasks_for_milestone(self, milestone: dict, milestone_template: dict, 
                                 domain_analysis: dict) -> list:
    """Generate 5 tasks for each milestone"""
    
    base_tasks = milestone_template.get("typical_tasks", [])
    requirements = domain_analysis["extracted_requirements"]
    
    tasks = []
    
    # Ensure we have exactly 5 tasks
    for i in range(5):
        if i < len(base_tasks):
            task_name = base_tasks[i]
        else:
            # Generate additional tasks if template has less than 5
            task_name = self._generate_additional_task(milestone, i, requirements)
        
        task = {
            "task_id": f"{milestone['milestone_id']}_T{i+1}",
            "name": task_name,
            "estimated_duration": self._estimate_task_duration(task_name, milestone),
            "complexity": self._assess_task_complexity(task_name, requirements),
            "required_skills": self._identify_required_skills(task_name),
            "dependencies": self._calculate_task_dependencies(i, milestone["milestone_id"]),
            "deliverables": self._define_task_deliverables(task_name),
            "acceptance_criteria": self._generate_task_acceptance_criteria(task_name)
        }
        
        tasks.append(task)
    
    return tasks

def _estimate_task_duration(self, task_name: str, milestone: dict) -> str:
    """Estimate task duration based on complexity and milestone duration"""
    
    # Parse milestone duration
    milestone_weeks = int(milestone["estimated_duration"].split("_")[0])
    
    # Task complexity indicators
    complexity_indicators = {
        "research": 0.8,
        "design": 1.0,
        "development": 1.2,
        "implementation": 1.2,
        "testing": 0.9,
        "integration": 1.1,
        "deployment": 0.7,
        "documentation": 0.6
    }
    
    # Determine base complexity
    base_complexity = 1.0
    for indicator, weight in complexity_indicators.items():
        if indicator in task_name.lower():
            base_complexity = weight
            break
    
    # Calculate task duration (assuming 5 tasks per milestone)
    task_days = math.ceil((milestone_weeks * 5 * base_complexity) / 5)
    
    if task_days <= 3:
        return f"{task_days}_days"
    else:
        task_weeks = math.ceil(task_days / 5)
        return f"{task_weeks}_weeks"

def _assess_task_complexity(self, task_name: str, requirements: dict) -> str:
    """Assess task complexity level"""
    
    high_complexity_indicators = [
        "integration", "security", "payment", "real-time", "scalability",
        "performance", "architecture", "complex", "advanced"
    ]
    
    medium_complexity_indicators = [
        "development", "implementation", "design", "testing", "api"
    ]
    
    task_lower = task_name.lower()
    
    if any(indicator in task_lower for indicator in high_complexity_indicators):
        return "high"
    elif any(indicator in task_lower for indicator in medium_complexity_indicators):
        return "medium"
    else:
        return "low"

def _identify_required_skills(self, task_name: str) -> list:
    """Identify required skills for task"""
    
    skill_mapping = {
        "research": ["business_analysis", "market_research"],
        "design": ["ui_design", "ux_design", "graphic_design"],
        "frontend": ["frontend_development", "javascript", "react"],
        "backend": ["backend_development", "api_development", "database"],
        "mobile": ["mobile_development", "ios", "android"],
        "testing": ["qa_testing", "automation_testing"],
        "deployment": ["devops", "cloud_deployment"],
        "security": ["security_engineering", "penetration_testing"]
    }
    
    identified_skills = []
    task_lower = task_name.lower()
    
    for keyword, skills in skill_mapping.items():
        if keyword in task_lower:
            identified_skills.extend(skills)
    
    return list(set(identified_skills)) if identified_skills else ["general_development"]
```

**📤 OUTPUT:** Task distribution algorithm với duration estimation và skill mapping
**⏱️ Duration:** 10 giờ

---

#### **Subtask 2.3.5: Implement Dependency Analysis**
**📥 INPUT:** Generated milestones và tasks, project constraints

**🔧 THỰC HIỆN:**
```python
def _analyze_dependencies(self, milestones: list) -> dict:
    """Analyze dependencies and create dependency graph"""
    
    dependency_graph = {}
    
    # Milestone-level dependencies
    for milestone in milestones:
        milestone_id = milestone["milestone_id"]
        dependency_graph[milestone_id] = milestone["dependencies"]
    
    # Task-level dependencies (within and across milestones)
    task_dependencies = {}
    
    for milestone in milestones:
        for task in milestone.get("tasks", []):
            task_id = task["task_id"]
            task_dependencies[task_id] = self._calculate_detailed_task_dependencies(
                task, milestone, milestones
            )
    
    return {
        "milestone_dependencies": dependency_graph,
        "task_dependencies": task_dependencies,
        "critical_path": self._identify_critical_path(milestones),
        "parallel_opportunities": self._identify_parallel_opportunities(milestones)
    }

def _identify_critical_path(self, milestones: list) -> list:
    """Identify critical path through milestones"""
    
    critical_milestones = []
    
    for milestone in milestones:
        if milestone["critical_path"]:
            critical_milestones.append(milestone["milestone_id"])
    
    return critical_milestones

def _identify_parallel_opportunities(self, milestones: list) -> list:
    """Identify tasks that can be executed in parallel"""
    
    parallel_opportunities = []
    
    # Look for tasks within same milestone that can run parallel
    for milestone in milestones:
        tasks = milestone.get("tasks", [])
        
        # Group tasks by type for parallel execution
        design_tasks = []
        development_tasks = []
        
        for task in tasks:
            task_name_lower = task["name"].lower()
            if "design" in task_name_lower or "wireframe" in task_name_lower:
                design_tasks.append(task["task_id"])
            elif "development" in task_name_lower or "implementation" in task_name_lower:
                development_tasks.append(task["task_id"])
        
        # If multiple tasks of same type, they can potentially run parallel
        if len(design_tasks) > 1:
            parallel_opportunities.append({
                "tasks": design_tasks,
                "type": "design_parallel",
                "milestone": milestone["milestone_id"],
                "estimated_time_saved": "3-5_days"
            })
        
        if len(development_tasks) > 1:
            parallel_opportunities.append({
                "tasks": development_tasks,
                "type": "development_parallel", 
                "milestone": milestone["milestone_id"],
                "estimated_time_saved": "1-2_weeks"
            })
    
    return parallel_opportunities
```

**📤 OUTPUT:** Comprehensive dependency analysis với critical path và parallel opportunities
**⏱️ Duration:** 6 giờ

---

#### **Subtask 2.3.6: Implement Optimization Scoring**
**📥 INPUT:** Complete structure với dependencies, optimization criteria

**🔧 THỰC HIỆN:**
```python
def _calculate_optimization_score(self, structure: dict, domain_analysis: dict) -> float:
    """Calculate overall optimization score for the structure"""
    
    scores = {
        "time_efficiency": self._score_time_efficiency(structure),
        "resource_balance": self._score_resource_balance(structure),
        "risk_mitigation": self._score_risk_mitigation(structure, domain_analysis),
        "dependency_optimization": self._score_dependency_optimization(structure)
    }
    
    # Weighted average
    total_score = 0
    for metric, weight in self.optimization_weights.items():
        total_score += scores[metric] * weight
    
    return round(total_score, 2)

def _score_time_efficiency(self, structure: dict) -> float:
    """Score time efficiency of the structure"""
    
    milestones = structure["milestone_structure"]
    parallel_ops = structure["dependency_analysis"]["parallel_opportunities"]
    
    # Base score from parallel opportunities
    parallel_score = min(len(parallel_ops) * 0.15, 0.6)
    
    # Score from balanced milestone durations
    durations = []
    for milestone in milestones:
        weeks = int(milestone["estimated_duration"].split("_")[0])
        durations.append(weeks)
    
    # Penalize if durations are too unbalanced
    duration_variance = max(durations) - min(durations)
    balance_score = max(0.4 - (duration_variance * 0.1), 0)
    
    return min(parallel_score + balance_score, 1.0)

def _score_resource_balance(self, structure: dict) -> float:
    """Score resource balance across milestones"""
    
    milestones = structure["milestone_structure"]
    
    # Analyze skill distribution
    skill_distribution = {}
    total_tasks = 0
    
    for milestone in milestones:
        for task in milestone.get("tasks", []):
            total_tasks += 1
            for skill in task.get("required_skills", []):
                skill_distribution[skill] = skill_distribution.get(skill, 0) + 1
    
    # Calculate balance score (avoid over-concentration of skills)
    if not skill_distribution:
        return 0.5
    
    max_skill_usage = max(skill_distribution.values())
    balance_ratio = max_skill_usage / total_tasks
    
    # Better balance = higher score
    balance_score = max(1.0 - balance_ratio, 0.3)
    
    return balance_score

def _score_risk_mitigation(self, structure: dict, domain_analysis: dict) -> float:
    """Score risk mitigation effectiveness"""
    
    complexity_level = domain_analysis["complexity_level"]
    milestones = structure["milestone_structure"]
    
    # Higher complexity projects need better risk mitigation
    complexity_weights = {
        "beginner": 0.7,
        "intermediate": 0.8,
        "advanced": 0.9,
        "expert": 1.0
    }
    
    base_weight = complexity_weights.get(complexity_level, 0.8)
    
    # Check for risk mitigation patterns
    risk_mitigation_score = 0.5  # Base score
    
    # Bonus for having testing milestone
    if any("testing" in m["name"].lower() for m in milestones):
        risk_mitigation_score += 0.2
    
    # Bonus for gradual complexity increase
    complexity_weights_list = [m["complexity_weight"] for m in milestones]
    if complexity_weights_list == sorted(complexity_weights_list):
        risk_mitigation_score += 0.2
    
    return min(risk_mitigation_score * base_weight, 1.0)
```

**📤 OUTPUT:** Optimization scoring system với multiple criteria
**⏱️ Duration:** 5 giờ

---

#### **Subtask 2.3.7: Implement Main Process Method**
**📥 INPUT:** Domain analysis từ Agent 1, all optimization logic

**🔧 THỰC HIỆN:**
```python
async def process(self, state: PlanGenerationState) -> dict:
    """Main structure optimization process"""
    
    domain_analysis = state["domain_analysis"]
    
    try:
        # Step 1: Select optimal template
        template = self._select_optimal_template(domain_analysis)
        
        # Step 2: Generate milestones
        milestones = self._generate_milestones(domain_analysis, template)
        
        # Step 3: Generate tasks for each milestone
        for milestone in milestones:
            milestone_template = next(
                (t for t in template["standard_flow"] if t["name"] == milestone["name"]), 
                template["standard_flow"][0]
            )
            milestone["tasks"] = self._generate_tasks_for_milestone(
                milestone, milestone_template, domain_analysis
            )
        
        # Step 4: Analyze dependencies
        dependency_analysis = self._analyze_dependencies(milestones)
        
        # Step 5: Create complete structure
        structure = {
            "milestone_structure": milestones,
            "dependency_graph": dependency_analysis["milestone_dependencies"],
            "task_dependencies": dependency_analysis["task_dependencies"],
            "critical_path_analysis": {
                "critical_milestones": dependency_analysis["critical_path"],
                "total_critical_duration": self._calculate_total_duration(milestones),
                "buffer_time": "1_week"
            },
            "parallel_opportunities": dependency_analysis["parallel_opportunities"]
        }
        
        # Step 6: Calculate optimization score
        structure["optimization_score"] = self._calculate_optimization_score(
            structure, domain_analysis
        )
        
        # Step 7: Add rationale
        structure["rationale"] = self._generate_optimization_rationale(
            structure, domain_analysis
        )
        
        # Step 8: Validate output
        if not self.validate_output({"structure_design": structure}):
            raise ValueError("Structure optimization validation failed")
        
        return {
            "structure_design": structure,
            "progress": 33.33  # 2/6 agents completed
        }
        
    except Exception as e:
        self.logger.error(f"Structure optimization failed: {e}")
        raise e

def validate_output(self, output: dict) -> bool:
    """Validate structure design output"""
    structure = output.get("structure_design", {})
    
    required_fields = [
        "milestone_structure", "dependency_graph", "critical_path_analysis",
        "optimization_score"
    ]
    
    if not all(field in structure for field in required_fields):
        return False
    
    # Validate milestone count
    milestones = structure.get("milestone_structure", [])
    if len(milestones) != 5:
        return False
    
    # Validate task count per milestone
    for milestone in milestones:
        tasks = milestone.get("tasks", [])
        if len(tasks) != 5:
            return False
    
    return True
```

**📤 OUTPUT:** Complete structure optimization process với validation
**⏱️ Duration:** 6 giờ

---

## 📊 **TỔNG KẾT AGENT 2**

### **Tổng thời gian ước tính:** 45 giờ (6-7 ngày làm việc)

### **Deliverables chính:**
1. **StructureOptimizationAgent class** - Complete implementation
2. **Domain-specific templates** - Milestone templates cho các domains
3. **Milestone generation** - 5 milestones với optimal structure
4. **Task distribution** - 25 tasks (5 per milestone) với duration estimates
5. **Dependency analysis** - Critical path và parallel opportunities
6. **Optimization scoring** - Multi-criteria optimization assessment
7. **Validation system** - Structure quality assurance

### **Input → Output Transformation:**
```
Domain Analysis (Agent 1 output)
    ↓
{
    "milestone_structure": [
        {
            "milestone_id": "M1",
            "name": "Research and Planning", 
            "estimated_duration": "2_weeks",
            "tasks": [5 detailed tasks với skills và dependencies]
        }
        // ... 4 more milestones
    ],
    "dependency_graph": {"M1": [], "M2": ["M1"], ...},
    "critical_path_analysis": {
        "critical_milestones": ["M1", "M2", "M3", "M5"],
        "total_duration": "12_weeks"
    },
    "parallel_opportunities": [...],
    "optimization_score": 0.89
}
```

**Agent 2 transforms domain analysis into structured, optimized project framework!** 🏗️
