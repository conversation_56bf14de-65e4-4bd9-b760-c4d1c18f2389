# ✅ **AGENT 5: VALIDATION AGENT - CHI TIẾT TASKS**

## 🎯 **TỔNG QUAN AGENT 5**

**Vai trò**: Quality Assurance Specialist ch<PERSON><PERSON><PERSON> validate plan quality và consistency
**Vị trí trong pipeline**: Agent thứ 5, nhận output từ tất cả agents trước
**<PERSON><PERSON><PERSON> tiêu chính**: Validate complete plan và detect issues trước khi enhancement

---

## 📋 **BREAKDOWN CHI TIẾT TASKS**

### **Task 4.1: Implement Validation Agent**
**<PERSON><PERSON><PERSON> tiêu**: Comprehensive validation của plan quality across multiple dimensions

#### **Subtask 4.1.1: Create Agent Class Structure**
**📥 INPUT:** BaseIgnitionAgent, validation requirements, quality standards

**🔧 THỰC HIỆN:**
```python
# agents/validation_agent.py
from multi_agent.base_agent import BaseIgnitionAgent
from multi_agent.utils.state_management import PlanGenerationState
from datetime import datetime, timedelta
import json
import re
from typing import Dict, List, Any, Tuple

class ValidationAgent(BaseIgnitionAgent):
    def __init__(self):
        super().__init__(
            agent_name="validation_agent",
            config={
                "primary_model": "gemini-2.0-flash-exp",
                "fallback_model": "gemini-1.5-pro",
                "temperature": 0.1,  # Very low for consistent validation
                "max_tokens": 2000,
                "timeout": 45
            }
        )
        self.validation_rules = self._load_validation_rules()
        self.quality_thresholds = self._load_quality_thresholds()
        self.auto_fix_enabled = True
        self.validation_weights = {
            "completeness": 0.25,
            "consistency": 0.25,
            "feasibility": 0.25,
            "requirement_alignment": 0.25
        }
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Main validation logic"""
        # Implementation sẽ được detail trong subtasks khác
        pass
```

**📤 OUTPUT:** ValidationAgent class với validation rules và thresholds
**⏱️ Duration:** 4 giờ

---

#### **Subtask 4.1.2: Load Validation Rules Engine**
**📥 INPUT:** Quality standards, validation best practices, configurable rules

**🔧 THỰC HIỆN:**
```python
def _load_validation_rules(self) -> dict:
    """Load comprehensive validation rules"""
    return {
        "structure_rules": {
            "milestone_count": {
                "min": 4,
                "max": 6,
                "optimal": 5,
                "severity": "high"
            },
            "task_count_per_milestone": {
                "min": 3,
                "max": 7,
                "optimal": 5,
                "severity": "high"
            },
            "subtask_count_per_task": {
                "min": 3,
                "max": 7,
                "optimal": 5,
                "severity": "medium"
            },
            "milestone_name_length": {
                "min_words": 5,
                "max_words": 20,
                "severity": "low"
            },
            "task_name_length": {
                "min_words": 6,
                "max_words": 25,
                "severity": "low"
            }
        },
        "content_rules": {
            "description_completeness": {
                "min_words": 20,
                "max_words": 100,
                "severity": "medium"
            },
            "actionable_language": {
                "required_verbs": ["create", "develop", "implement", "build", "design", "analyze", "test"],
                "severity": "medium"
            },
            "acceptance_criteria_presence": {
                "required": True,
                "severity": "high"
            },
            "tools_specification": {
                "min_tools": 2,
                "severity": "low"
            }
        },
        "timeline_rules": {
            "total_duration_bounds": {
                "min_weeks": 4,
                "max_weeks": 52,
                "severity": "high"
            },
            "milestone_duration_balance": {
                "max_variance_ratio": 3.0,  # Longest milestone shouldn't be 3x longer than shortest
                "severity": "medium"
            },
            "buffer_time_presence": {
                "min_percentage": 0.05,  # 5% minimum buffer
                "severity": "medium"
            },
            "working_days_validation": {
                "exclude_weekends": True,
                "severity": "low"
            }
        },
        "consistency_rules": {
            "naming_consistency": {
                "emoji_usage": "consistent",  # All or none
                "capitalization": "consistent",
                "severity": "low"
            },
            "duration_format_consistency": {
                "format_pattern": r"\d+_(days|weeks|months)",
                "severity": "medium"
            },
            "skill_requirement_consistency": {
                "valid_skills": [
                    "business_analysis", "ui_design", "ux_design", "frontend_development",
                    "backend_development", "mobile_development", "qa_testing", "devops",
                    "project_management", "data_analysis", "security_engineering"
                ],
                "severity": "medium"
            }
        },
        "feasibility_rules": {
            "resource_allocation": {
                "max_concurrent_high_intensity": 2,
                "severity": "high"
            },
            "skill_availability": {
                "max_parallel_same_skill": 3,
                "severity": "medium"
            },
            "dependency_cycles": {
                "allowed": False,
                "severity": "high"
            },
            "critical_path_length": {
                "max_percentage": 0.8,  # Critical path shouldn't be >80% of total
                "severity": "medium"
            }
        }
    }

def _load_quality_thresholds(self) -> dict:
    """Load quality thresholds for validation"""
    return {
        "completeness_score": 0.90,
        "consistency_score": 0.85,
        "feasibility_score": 0.80,
        "requirement_alignment": 0.85,
        "overall_quality": 0.85,
        "content_quality": {
            "clarity_score": 0.80,
            "actionability_score": 0.75,
            "engagement_score": 0.70
        },
        "timeline_quality": {
            "realism_score": 0.80,
            "optimization_score": 0.75
        }
    }
```

**📤 OUTPUT:** Comprehensive validation rules engine với configurable thresholds
**⏱️ Duration:** 6 giờ

---

#### **Subtask 4.1.3: Implement Completeness Validation**
**📥 INPUT:** Complete plan data, completeness rules, required fields

**🔧 THỰC HIỆN:**
```python
def _validate_completeness(self, plan_data: dict) -> dict:
    """Validate completeness of plan across all dimensions"""
    
    completeness_issues = []
    completeness_score = 1.0
    
    # 1. Structure completeness
    structure_issues, structure_score = self._validate_structure_completeness(plan_data)
    completeness_issues.extend(structure_issues)
    
    # 2. Content completeness
    content_issues, content_score = self._validate_content_completeness(plan_data)
    completeness_issues.extend(content_issues)
    
    # 3. Timeline completeness
    timeline_issues, timeline_score = self._validate_timeline_completeness(plan_data)
    completeness_issues.extend(timeline_issues)
    
    # 4. Metadata completeness
    metadata_issues, metadata_score = self._validate_metadata_completeness(plan_data)
    completeness_issues.extend(metadata_issues)
    
    # Calculate overall completeness score
    overall_score = (structure_score + content_score + timeline_score + metadata_score) / 4
    
    return {
        "completeness_score": overall_score,
        "issues_found": completeness_issues,
        "dimension_scores": {
            "structure_completeness": structure_score,
            "content_completeness": content_score,
            "timeline_completeness": timeline_score,
            "metadata_completeness": metadata_score
        }
    }

def _validate_structure_completeness(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate structural completeness"""
    
    issues = []
    score = 1.0
    
    # Check milestone count
    milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    milestone_count = len(milestones)
    
    rules = self.validation_rules["structure_rules"]
    
    if milestone_count < rules["milestone_count"]["min"]:
        issues.append({
            "issue_id": "STRUCT_001",
            "type": "insufficient_milestones",
            "severity": rules["milestone_count"]["severity"],
            "description": f"Plan has {milestone_count} milestones, minimum required is {rules['milestone_count']['min']}",
            "location": "plan_structure",
            "auto_fixable": False,
            "suggested_fix": f"Add {rules['milestone_count']['min'] - milestone_count} more milestones"
        })
        score -= 0.3
    
    elif milestone_count > rules["milestone_count"]["max"]:
        issues.append({
            "issue_id": "STRUCT_002",
            "type": "excessive_milestones",
            "severity": rules["milestone_count"]["severity"],
            "description": f"Plan has {milestone_count} milestones, maximum recommended is {rules['milestone_count']['max']}",
            "location": "plan_structure",
            "auto_fixable": False,
            "suggested_fix": f"Consider consolidating {milestone_count - rules['milestone_count']['max']} milestones"
        })
        score -= 0.2
    
    # Check task count per milestone
    for i, milestone in enumerate(milestones):
        tasks = milestone.get("tasks", [])
        task_count = len(tasks)
        
        if task_count < rules["task_count_per_milestone"]["min"]:
            issues.append({
                "issue_id": f"STRUCT_003_{i}",
                "type": "insufficient_tasks",
                "severity": rules["task_count_per_milestone"]["severity"],
                "description": f"Milestone {milestone.get('milestone_id', i+1)} has {task_count} tasks, minimum is {rules['task_count_per_milestone']['min']}",
                "location": f"milestone_{milestone.get('milestone_id', i+1)}",
                "auto_fixable": True,
                "suggested_fix": f"Add {rules['task_count_per_milestone']['min'] - task_count} more tasks"
            })
            score -= 0.1
        
        # Check subtask count per task
        for j, task in enumerate(tasks):
            subtasks = task.get("subtasks", [])
            subtask_count = len(subtasks)
            
            if subtask_count < rules["subtask_count_per_task"]["min"]:
                issues.append({
                    "issue_id": f"STRUCT_004_{i}_{j}",
                    "type": "insufficient_subtasks",
                    "severity": rules["subtask_count_per_task"]["severity"],
                    "description": f"Task {task.get('task_id', f'T{j+1}')} has {subtask_count} subtasks, minimum is {rules['subtask_count_per_task']['min']}",
                    "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_{j+1}",
                    "auto_fixable": True,
                    "suggested_fix": f"Add {rules['subtask_count_per_task']['min'] - subtask_count} more subtasks"
                })
                score -= 0.05
    
    return issues, max(score, 0.0)

def _validate_content_completeness(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate content completeness"""
    
    issues = []
    score = 1.0
    
    content_rules = self.validation_rules["content_rules"]
    milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    
    for i, milestone in enumerate(milestones):
        # Check milestone description
        description = milestone.get("description", "")
        word_count = len(description.split())
        
        if word_count < content_rules["description_completeness"]["min_words"]:
            issues.append({
                "issue_id": f"CONTENT_001_{i}",
                "type": "insufficient_description",
                "severity": content_rules["description_completeness"]["severity"],
                "description": f"Milestone description has {word_count} words, minimum is {content_rules['description_completeness']['min_words']}",
                "location": f"milestone_{milestone.get('milestone_id', i+1)}.description",
                "auto_fixable": True,
                "suggested_fix": "Expand description with more context and details"
            })
            score -= 0.1
        
        # Check tasks content
        for j, task in enumerate(milestone.get("tasks", [])):
            # Check acceptance criteria presence
            if not task.get("acceptance_criteria") and content_rules["acceptance_criteria_presence"]["required"]:
                issues.append({
                    "issue_id": f"CONTENT_002_{i}_{j}",
                    "type": "missing_acceptance_criteria",
                    "severity": content_rules["acceptance_criteria_presence"]["severity"],
                    "description": f"Task {task.get('name', f'Task {j+1}')} missing acceptance criteria",
                    "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_{j+1}",
                    "auto_fixable": True,
                    "suggested_fix": "Add clear acceptance criteria for task completion"
                })
                score -= 0.15
            
            # Check subtasks content
            for k, subtask in enumerate(task.get("subtasks", [])):
                # Check actionable steps presence
                actionable_steps = subtask.get("actionable_steps", [])
                if len(actionable_steps) < 3:
                    issues.append({
                        "issue_id": f"CONTENT_003_{i}_{j}_{k}",
                        "type": "insufficient_actionable_steps",
                        "severity": "medium",
                        "description": f"Subtask has {len(actionable_steps)} actionable steps, minimum is 3",
                        "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_{j+1}.subtask_{k+1}",
                        "auto_fixable": True,
                        "suggested_fix": "Add more specific actionable steps"
                    })
                    score -= 0.05
                
                # Check tools specification
                tools_needed = subtask.get("tools_needed", [])
                if len(tools_needed) < content_rules["tools_specification"]["min_tools"]:
                    issues.append({
                        "issue_id": f"CONTENT_004_{i}_{j}_{k}",
                        "type": "insufficient_tools_specification",
                        "severity": content_rules["tools_specification"]["severity"],
                        "description": f"Subtask specifies {len(tools_needed)} tools, minimum is {content_rules['tools_specification']['min_tools']}",
                        "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_{j+1}.subtask_{k+1}",
                        "auto_fixable": True,
                        "suggested_fix": "Specify required tools and software"
                    })
                    score -= 0.03
    
    return issues, max(score, 0.0)

def _validate_timeline_completeness(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate timeline completeness"""
    
    issues = []
    score = 1.0
    
    timeline_data = plan_data.get("timeline_data", {})
    timeline_rules = self.validation_rules["timeline_rules"]
    
    # Check essential timeline fields
    required_fields = ["total_duration", "start_date", "end_date", "milestones"]
    for field in required_fields:
        if field not in timeline_data:
            issues.append({
                "issue_id": f"TIMELINE_001_{field}",
                "type": "missing_timeline_field",
                "severity": "high",
                "description": f"Timeline missing required field: {field}",
                "location": "timeline_data",
                "auto_fixable": False,
                "suggested_fix": f"Add {field} to timeline data"
            })
            score -= 0.2
    
    # Check milestone timeline completeness
    milestones = timeline_data.get("milestones", [])
    for i, milestone in enumerate(milestones):
        required_milestone_fields = ["start_date", "end_date", "duration_days", "tasks"]
        for field in required_milestone_fields:
            if field not in milestone:
                issues.append({
                    "issue_id": f"TIMELINE_002_{i}_{field}",
                    "type": "missing_milestone_timeline_field",
                    "severity": "medium",
                    "description": f"Milestone {milestone.get('milestone_id', i+1)} missing {field}",
                    "location": f"timeline.milestone_{i+1}",
                    "auto_fixable": True,
                    "suggested_fix": f"Calculate and add {field} for milestone"
                })
                score -= 0.1
    
    return issues, max(score, 0.0)

def _validate_metadata_completeness(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate metadata completeness"""
    
    issues = []
    score = 1.0
    
    # Check domain analysis metadata
    domain_analysis = plan_data.get("domain_analysis", {})
    required_domain_fields = ["primary_domain", "complexity_level", "extracted_requirements", "constraints"]
    
    for field in required_domain_fields:
        if field not in domain_analysis:
            issues.append({
                "issue_id": f"META_001_{field}",
                "type": "missing_domain_metadata",
                "severity": "high",
                "description": f"Domain analysis missing {field}",
                "location": "domain_analysis",
                "auto_fixable": False,
                "suggested_fix": f"Add {field} to domain analysis"
            })
            score -= 0.2
    
    # Check content metadata
    content_data = plan_data.get("content_data", {})
    if "content_metrics" not in content_data:
        issues.append({
            "issue_id": "META_002",
            "type": "missing_content_metrics",
            "severity": "medium",
            "description": "Content data missing quality metrics",
            "location": "content_data",
            "auto_fixable": True,
            "suggested_fix": "Calculate and add content quality metrics"
        })
        score -= 0.1
    
    return issues, max(score, 0.0)
```

**📤 OUTPUT:** Comprehensive completeness validation với detailed issue detection
**⏱️ Duration:** 10 giờ

---

#### **Subtask 4.1.4: Implement Consistency Validation**
**📥 INPUT:** Plan data, consistency rules, format standards

**🔧 THỰC HIỆN:**
```python
def _validate_consistency(self, plan_data: dict) -> dict:
    """Validate consistency across all plan elements"""
    
    consistency_issues = []
    consistency_score = 1.0
    
    # 1. Naming consistency
    naming_issues, naming_score = self._validate_naming_consistency(plan_data)
    consistency_issues.extend(naming_issues)
    
    # 2. Format consistency
    format_issues, format_score = self._validate_format_consistency(plan_data)
    consistency_issues.extend(format_issues)
    
    # 3. Data consistency
    data_issues, data_score = self._validate_data_consistency(plan_data)
    consistency_issues.extend(data_issues)
    
    # 4. Cross-reference consistency
    cross_ref_issues, cross_ref_score = self._validate_cross_reference_consistency(plan_data)
    consistency_issues.extend(cross_ref_issues)
    
    overall_score = (naming_score + format_score + data_score + cross_ref_score) / 4
    
    return {
        "consistency_score": overall_score,
        "issues_found": consistency_issues,
        "dimension_scores": {
            "naming_consistency": naming_score,
            "format_consistency": format_score,
            "data_consistency": data_score,
            "cross_reference_consistency": cross_ref_score
        }
    }

def _validate_naming_consistency(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate naming consistency across plan elements"""
    
    issues = []
    score = 1.0
    
    milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    
    # Check emoji usage consistency
    milestone_names = [m.get("name", "") for m in milestones]
    emoji_usage = [bool(re.search(r'[🎯🚀📊🔍⚡🧪🎨🔗📋]', name)) for name in milestone_names]
    
    if any(emoji_usage) and not all(emoji_usage):
        issues.append({
            "issue_id": "CONSIST_001",
            "type": "inconsistent_emoji_usage",
            "severity": "low",
            "description": f"Emoji usage inconsistent: {sum(emoji_usage)}/{len(emoji_usage)} milestones use emojis",
            "location": "milestone_names",
            "auto_fixable": True,
            "suggested_fix": "Either add emojis to all milestones or remove from all"
        })
        score -= 0.1
    
    # Check capitalization consistency
    capitalization_patterns = []
    for name in milestone_names:
        if name:
            # Check if title case, sentence case, or other
            words = name.split()
            if all(word[0].isupper() for word in words if word and word[0].isalpha()):
                capitalization_patterns.append("title_case")
            elif name[0].isupper() and all(word[0].islower() for word in words[1:] if word and word[0].isalpha()):
                capitalization_patterns.append("sentence_case")
            else:
                capitalization_patterns.append("mixed")
    
    if len(set(capitalization_patterns)) > 1:
        issues.append({
            "issue_id": "CONSIST_002",
            "type": "inconsistent_capitalization",
            "severity": "low",
            "description": f"Inconsistent capitalization patterns: {set(capitalization_patterns)}",
            "location": "milestone_names",
            "auto_fixable": True,
            "suggested_fix": "Standardize to title case or sentence case"
        })
        score -= 0.1
    
    # Check task naming consistency within milestones
    for i, milestone in enumerate(milestones):
        tasks = milestone.get("tasks", [])
        task_names = [t.get("name", "") for t in tasks]
        
        # Check if all tasks start with action verbs
        action_verbs = ["create", "develop", "implement", "build", "design", "conduct", "analyze", "test", "deploy"]
        tasks_with_action_verbs = [
            any(name.lower().startswith(verb) for verb in action_verbs) 
            for name in task_names
        ]
        
        if any(tasks_with_action_verbs) and not all(tasks_with_action_verbs):
            issues.append({
                "issue_id": f"CONSIST_003_{i}",
                "type": "inconsistent_task_naming",
                "severity": "medium",
                "description": f"Milestone {milestone.get('milestone_id', i+1)}: {sum(tasks_with_action_verbs)}/{len(tasks_with_action_verbs)} tasks start with action verbs",
                "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_names",
                "auto_fixable": True,
                "suggested_fix": "Ensure all tasks start with action verbs"
            })
            score -= 0.05
    
    return issues, max(score, 0.0)

def _validate_format_consistency(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate format consistency"""
    
    issues = []
    score = 1.0
    
    # Check duration format consistency
    timeline_data = plan_data.get("timeline_data", {})
    duration_pattern = re.compile(r'\d+_(days|weeks|months)')
    
    # Check total duration format
    total_duration = timeline_data.get("total_duration", "")
    if total_duration and not duration_pattern.match(total_duration):
        issues.append({
            "issue_id": "FORMAT_001",
            "type": "invalid_duration_format",
            "severity": "medium",
            "description": f"Total duration '{total_duration}' doesn't match expected format 'N_unit'",
            "location": "timeline_data.total_duration",
            "auto_fixable": True,
            "suggested_fix": "Format as 'number_unit' (e.g., '12_weeks')"
        })
        score -= 0.1
    
    # Check milestone duration formats
    milestones = timeline_data.get("milestones", [])
    for i, milestone in enumerate(milestones):
        duration_fields = ["duration_days", "duration_weeks"]
        for field in duration_fields:
            if field in milestone:
                value = str(milestone[field])
                if not value.isdigit() and not duration_pattern.match(value):
                    issues.append({
                        "issue_id": f"FORMAT_002_{i}_{field}",
                        "type": "invalid_milestone_duration_format",
                        "severity": "medium",
                        "description": f"Milestone {milestone.get('milestone_id', i+1)} {field} format invalid: '{value}'",
                        "location": f"timeline.milestone_{i+1}.{field}",
                        "auto_fixable": True,
                        "suggested_fix": "Use numeric value or 'N_unit' format"
                    })
                    score -= 0.05
    
    # Check date format consistency
    date_pattern = re.compile(r'\d{4}-\d{2}-\d{2}')
    date_fields = ["start_date", "end_date"]
    
    for field in date_fields:
        if field in timeline_data:
            date_value = timeline_data[field]
            if not date_pattern.match(str(date_value)):
                issues.append({
                    "issue_id": f"FORMAT_003_{field}",
                    "type": "invalid_date_format",
                    "severity": "high",
                    "description": f"Date field '{field}' has invalid format: '{date_value}'",
                    "location": f"timeline_data.{field}",
                    "auto_fixable": True,
                    "suggested_fix": "Use ISO date format 'YYYY-MM-DD'"
                })
                score -= 0.15
    
    return issues, max(score, 0.0)

def _validate_data_consistency(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate data consistency across different sections"""
    
    issues = []
    score = 1.0
    
    # Check milestone count consistency
    content_milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    timeline_milestones = plan_data.get("timeline_data", {}).get("milestones", [])
    structure_milestones = plan_data.get("structure_design", {}).get("milestone_structure", [])
    
    counts = [len(content_milestones), len(timeline_milestones), len(structure_milestones)]
    if len(set(counts)) > 1:
        issues.append({
            "issue_id": "DATA_001",
            "type": "milestone_count_mismatch",
            "severity": "high",
            "description": f"Milestone count mismatch: content={counts[0]}, timeline={counts[1]}, structure={counts[2]}",
            "location": "cross_section_data",
            "auto_fixable": False,
            "suggested_fix": "Ensure all sections have same number of milestones"
        })
        score -= 0.3
    
    # Check milestone ID consistency
    if content_milestones and timeline_milestones:
        content_ids = [m.get("milestone_id", "") for m in content_milestones]
        timeline_ids = [m.get("milestone_id", "") for m in timeline_milestones]
        
        if content_ids != timeline_ids:
            issues.append({
                "issue_id": "DATA_002",
                "type": "milestone_id_mismatch",
                "severity": "high",
                "description": f"Milestone IDs don't match between content and timeline",
                "location": "milestone_ids",
                "auto_fixable": True,
                "suggested_fix": "Synchronize milestone IDs across all sections"
            })
            score -= 0.2
    
    # Check task count consistency
    for i, (content_milestone, timeline_milestone) in enumerate(zip(content_milestones, timeline_milestones)):
        content_task_count = len(content_milestone.get("tasks", []))
        timeline_task_count = len(timeline_milestone.get("tasks", []))
        
        if content_task_count != timeline_task_count:
            issues.append({
                "issue_id": f"DATA_003_{i}",
                "type": "task_count_mismatch",
                "severity": "medium",
                "description": f"Milestone {content_milestone.get('milestone_id', i+1)}: content has {content_task_count} tasks, timeline has {timeline_task_count}",
                "location": f"milestone_{i+1}_tasks",
                "auto_fixable": True,
                "suggested_fix": "Synchronize task counts between content and timeline"
            })
            score -= 0.1
    
    return issues, max(score, 0.0)
```

**📤 OUTPUT:** Consistency validation với cross-section data verification
**⏱️ Duration:** 8 giờ

---

#### **Subtask 4.1.5: Implement Feasibility Assessment**
**📥 INPUT:** Timeline data, resource constraints, domain complexity

**🔧 THỰC HIỆN:**
```python
def _validate_feasibility(self, plan_data: dict) -> dict:
    """Validate feasibility of plan execution"""
    
    feasibility_issues = []
    feasibility_score = 1.0
    
    # 1. Timeline feasibility
    timeline_issues, timeline_score = self._validate_timeline_feasibility(plan_data)
    feasibility_issues.extend(timeline_issues)
    
    # 2. Resource feasibility
    resource_issues, resource_score = self._validate_resource_feasibility(plan_data)
    feasibility_issues.extend(resource_issues)
    
    # 3. Technical feasibility
    technical_issues, technical_score = self._validate_technical_feasibility(plan_data)
    feasibility_issues.extend(technical_issues)
    
    # 4. Dependency feasibility
    dependency_issues, dependency_score = self._validate_dependency_feasibility(plan_data)
    feasibility_issues.extend(dependency_issues)
    
    overall_score = (timeline_score + resource_score + technical_score + dependency_score) / 4
    
    return {
        "feasibility_score": overall_score,
        "issues_found": feasibility_issues,
        "dimension_scores": {
            "timeline_feasibility": timeline_score,
            "resource_feasibility": resource_score,
            "technical_feasibility": technical_score,
            "dependency_feasibility": dependency_score
        }
    }

def _validate_timeline_feasibility(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate timeline feasibility"""
    
    issues = []
    score = 1.0
    
    timeline_data = plan_data.get("timeline_data", {})
    domain_analysis = plan_data.get("domain_analysis", {})
    
    # Check total duration reasonableness
    total_duration = timeline_data.get("total_duration", "")
    if total_duration:
        weeks = int(total_duration.split("_")[0]) if "_" in total_duration else 0
        complexity = domain_analysis.get("complexity_level", "intermediate")
        
        # Define reasonable duration ranges by complexity
        duration_ranges = {
            "beginner": {"min": 4, "max": 16},
            "intermediate": {"min": 8, "max": 24},
            "advanced": {"min": 12, "max": 36},
            "expert": {"min": 16, "max": 52}
        }
        
        range_info = duration_ranges.get(complexity, duration_ranges["intermediate"])
        
        if weeks < range_info["min"]:
            issues.append({
                "issue_id": "FEASIBLE_001",
                "type": "timeline_too_aggressive",
                "severity": "high",
                "description": f"{weeks} weeks may be too aggressive for {complexity} complexity project",
                "location": "timeline_data.total_duration",
                "auto_fixable": True,
                "suggested_fix": f"Consider extending to at least {range_info['min']} weeks"
            })
            score -= 0.3
        
        elif weeks > range_info["max"]:
            issues.append({
                "issue_id": "FEASIBLE_002",
                "type": "timeline_too_conservative",
                "severity": "medium",
                "description": f"{weeks} weeks may be too conservative for {complexity} complexity project",
                "location": "timeline_data.total_duration",
                "auto_fixable": True,
                "suggested_fix": f"Consider optimizing to around {range_info['max']} weeks"
            })
            score -= 0.1
    
    # Check milestone duration balance
    milestones = timeline_data.get("milestones", [])
    if milestones:
        durations = [m.get("duration_weeks", 0) for m in milestones]
        if durations:
            max_duration = max(durations)
            min_duration = min(durations)
            
            if max_duration > min_duration * 3:  # More than 3x difference
                issues.append({
                    "issue_id": "FEASIBLE_003",
                    "type": "unbalanced_milestone_durations",
                    "severity": "medium",
                    "description": f"Large duration variance: longest={max_duration}w, shortest={min_duration}w",
                    "location": "milestone_durations",
                    "auto_fixable": True,
                    "suggested_fix": "Rebalance milestone scope for more even distribution"
                })
                score -= 0.15
    
    return issues, max(score, 0.0)

def _validate_resource_feasibility(self, plan_data: dict) -> Tuple[List[dict], float]:
    """Validate resource allocation feasibility"""
    
    issues = []
    score = 1.0
    
    timeline_data = plan_data.get("timeline_data", {})
    domain_analysis = plan_data.get("domain_analysis", {})
    
    # Check team size vs workload
    team_size = domain_analysis.get("constraints", {}).get("team_size", "small_team_3_5_people")
    milestones = timeline_data.get("milestones", [])
    
    # Calculate total effort hours
    total_effort = 0
    for milestone in milestones:
        for task in milestone.get("tasks", []):
            total_effort += task.get("effort_hours", 40)
    
    # Estimate required team size
    total_weeks = len(milestones) * 3  # Rough estimate
    hours_per_week_per_person = 40
    required_people = total_effort / (total_weeks * hours_per_week_per_person)
    
    team_size_mapping = {
        "solo": 1,
        "small_team_3_5_people": 4,
        "medium_team_6_10_people": 8,
        "large_team": 12
    }
    
    available_people = team_size_mapping.get(team_size, 4)
    
    if required_people > available_people * 1.2:  # 20% buffer
        issues.append({
            "issue_id": "RESOURCE_001",
            "type": "insufficient_team_size",
            "severity": "high",
            "description": f"Estimated {required_people:.1f} people needed, but team size is {team_size} ({available_people} people)",
            "location": "resource_allocation",
            "auto_fixable": False,
            "suggested_fix": f"Consider increasing team size or extending timeline"
        })
        score -= 0.3
    
    # Check for resource conflicts
    high_intensity_milestones = [
        m for m in milestones 
        if m.get("resource_intensity") in ["high", "very_high"]
    ]
    
    if len(high_intensity_milestones) > 2:
        issues.append({
            "issue_id": "RESOURCE_002",
            "type": "excessive_high_intensity_periods",
            "severity": "medium",
            "description": f"{len(high_intensity_milestones)} high-intensity milestones may cause team burnout",
            "location": "milestone_intensity",
            "auto_fixable": True,
            "suggested_fix": "Distribute high-intensity work more evenly or add recovery periods"
        })
        score -= 0.2
    
    return issues, max(score, 0.0)
```

**📤 OUTPUT:** Feasibility assessment với timeline, resource, và technical validation
**⏱️ Duration:** 9 giờ

---

#### **Subtask 4.1.6: Implement Auto-Fix Capabilities**
**📥 INPUT:** Detected issues, auto-fix rules, plan data

**🔧 THỰC HIỆN:**
```python
def _apply_auto_fixes(self, issues: list, plan_data: dict) -> dict:
    """Apply automatic fixes for issues that can be resolved automatically"""
    
    if not self.auto_fix_enabled:
        return {"fixes_applied": [], "plan_data": plan_data}
    
    fixes_applied = []
    modified_plan_data = plan_data.copy()
    
    for issue in issues:
        if issue.get("auto_fixable", False):
            fix_result = self._apply_single_fix(issue, modified_plan_data)
            if fix_result["success"]:
                fixes_applied.append({
                    "issue_id": issue["issue_id"],
                    "issue_type": issue["type"],
                    "fix_applied": fix_result["fix_description"],
                    "location": issue["location"]
                })
                modified_plan_data = fix_result["modified_data"]
    
    return {
        "fixes_applied": fixes_applied,
        "plan_data": modified_plan_data,
        "fix_success_rate": len(fixes_applied) / len([i for i in issues if i.get("auto_fixable", False)]) if any(i.get("auto_fixable", False) for i in issues) else 0
    }

def _apply_single_fix(self, issue: dict, plan_data: dict) -> dict:
    """Apply a single automatic fix"""
    
    try:
        issue_type = issue["type"]
        location = issue["location"]
        
        if issue_type == "insufficient_subtasks":
            return self._fix_insufficient_subtasks(issue, plan_data)
        elif issue_type == "missing_acceptance_criteria":
            return self._fix_missing_acceptance_criteria(issue, plan_data)
        elif issue_type == "insufficient_actionable_steps":
            return self._fix_insufficient_actionable_steps(issue, plan_data)
        elif issue_type == "inconsistent_emoji_usage":
            return self._fix_inconsistent_emoji_usage(issue, plan_data)
        elif issue_type == "invalid_duration_format":
            return self._fix_invalid_duration_format(issue, plan_data)
        elif issue_type == "milestone_id_mismatch":
            return self._fix_milestone_id_mismatch(issue, plan_data)
        else:
            return {"success": False, "reason": f"No auto-fix available for {issue_type}"}
    
    except Exception as e:
        return {"success": False, "reason": f"Auto-fix failed: {str(e)}"}

def _fix_insufficient_subtasks(self, issue: dict, plan_data: dict) -> dict:
    """Fix insufficient subtasks by generating additional ones"""
    
    # Parse location to find the specific task
    location_parts = issue["location"].split(".")
    milestone_id = location_parts[0].split("_")[1]
    task_index = int(location_parts[1].split("_")[1]) - 1
    
    # Find the milestone and task
    milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    
    for milestone in milestones:
        if milestone.get("milestone_id") == milestone_id:
            tasks = milestone.get("tasks", [])
            if task_index < len(tasks):
                task = tasks[task_index]
                current_subtasks = task.get("subtasks", [])
                needed_subtasks = 5 - len(current_subtasks)
                
                # Generate additional subtasks
                for i in range(needed_subtasks):
                    new_subtask = {
                        "subtask_id": f"{task.get('task_id', 'T1')}_ST{len(current_subtasks) + i + 1}",
                        "name": f"Complete additional task component {i + 1}",
                        "description": "Additional task component to ensure comprehensive completion",
                        "actionable_steps": [
                            "Review task requirements and identify missing components",
                            "Plan approach for completing this component",
                            "Execute the planned approach with attention to quality"
                        ],
                        "expected_outcome": "Task component completed according to specifications",
                        "time_estimate": "4 hours",
                        "tools_needed": ["Standard development tools", "Documentation software"]
                    }
                    current_subtasks.append(new_subtask)
                
                task["subtasks"] = current_subtasks
                
                return {
                    "success": True,
                    "fix_description": f"Added {needed_subtasks} subtasks to reach minimum of 5",
                    "modified_data": plan_data
                }
    
    return {"success": False, "reason": "Could not locate task to fix"}

def _fix_missing_acceptance_criteria(self, issue: dict, plan_data: dict) -> dict:
    """Fix missing acceptance criteria"""
    
    # Parse location and find task
    location_parts = issue["location"].split(".")
    milestone_id = location_parts[0].split("_")[1]
    task_index = int(location_parts[1].split("_")[1]) - 1
    
    milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    
    for milestone in milestones:
        if milestone.get("milestone_id") == milestone_id:
            tasks = milestone.get("tasks", [])
            if task_index < len(tasks):
                task = tasks[task_index]
                
                # Generate generic acceptance criteria based on task name
                task_name = task.get("name", "").lower()
                
                if "research" in task_name:
                    criteria = "Research completed with documented findings, analysis report created, and recommendations provided"
                elif "development" in task_name or "implement" in task_name:
                    criteria = "Feature implemented according to specifications, tested and working correctly, code reviewed and documented"
                elif "design" in task_name:
                    criteria = "Design mockups completed, user flow documented, design system components created and approved"
                elif "testing" in task_name:
                    criteria = "Test cases executed, bugs identified and documented, quality standards met and signed off"
                else:
                    criteria = "Task completed according to specifications, deliverables meet quality standards, stakeholder approval obtained"
                
                task["acceptance_criteria"] = criteria
                
                return {
                    "success": True,
                    "fix_description": "Added appropriate acceptance criteria based on task type",
                    "modified_data": plan_data
                }
    
    return {"success": False, "reason": "Could not locate task to fix"}
```

**📤 OUTPUT:** Auto-fix system với intelligent issue resolution
**⏱️ Duration:** 8 giờ

---

#### **Subtask 4.1.7: Implement Main Process Method**
**📥 INPUT:** Complete plan data từ tất cả agents trước

**🔧 THỰC HIỆN:**
```python
async def process(self, state: PlanGenerationState) -> dict:
    """Main validation process"""
    
    # Collect all plan data
    plan_data = {
        "domain_analysis": state.get("domain_analysis", {}),
        "structure_design": state.get("structure_design", {}),
        "content_data": state.get("content_data", {}),
        "timeline_data": state.get("timeline_data", {})
    }
    
    try:
        # Step 1: Validate completeness
        completeness_results = self._validate_completeness(plan_data)
        
        # Step 2: Validate consistency
        consistency_results = self._validate_consistency(plan_data)
        
        # Step 3: Validate feasibility
        feasibility_results = self._validate_feasibility(plan_data)
        
        # Step 4: Validate requirement alignment
        alignment_results = self._validate_requirement_alignment(plan_data)
        
        # Step 5: Collect all issues
        all_issues = []
        all_issues.extend(completeness_results["issues_found"])
        all_issues.extend(consistency_results["issues_found"])
        all_issues.extend(feasibility_results["issues_found"])
        all_issues.extend(alignment_results["issues_found"])
        
        # Step 6: Apply auto-fixes
        auto_fix_results = self._apply_auto_fixes(all_issues, plan_data)
        
        # Step 7: Generate improvement suggestions
        improvements = self._generate_improvement_suggestions(all_issues, plan_data)
        
        # Step 8: Calculate overall scores
        dimension_scores = {
            "completeness_score": completeness_results["completeness_score"],
            "consistency_score": consistency_results["consistency_score"],
            "feasibility_score": feasibility_results["feasibility_score"],
            "requirement_alignment": alignment_results["alignment_score"]
        }
        
        overall_score = sum(
            score * weight for score, weight in zip(
                dimension_scores.values(), 
                self.validation_weights.values()
            )
        )
        
        # Step 9: Determine quality gates
        quality_gates = self._evaluate_quality_gates(dimension_scores)
        
        # Step 10: Create validation results
        validation_results = {
            "overall_score": overall_score,
            "validation_timestamp": datetime.now().isoformat(),
            "dimension_scores": dimension_scores,
            "issues_found": [issue for issue in all_issues if issue["issue_id"] not in [fix["issue_id"] for fix in auto_fix_results["fixes_applied"]]],
            "auto_fixes_applied": auto_fix_results["fixes_applied"],
            "improvements_suggested": improvements,
            "quality_gates": quality_gates,
            "compliance_check": {
                "ignition_standards": "compliant" if overall_score >= 0.85 else "non_compliant",
                "project_management_best_practices": "compliant" if feasibility_results["feasibility_score"] >= 0.8 else "needs_improvement",
                "technical_feasibility": "compliant" if feasibility_results["dimension_scores"]["technical_feasibility"] >= 0.8 else "needs_review"
            }
        }
        
        # Step 11: Validate output
        if not self.validate_output({"validation_results": validation_results}):
            raise ValueError("Validation results validation failed")
        
        return {
            "validation_results": validation_results,
            "progress": 83.33  # 5/6 agents completed
        }
        
    except Exception as e:
        self.logger.error(f"Validation failed: {e}")
        raise e

def _evaluate_quality_gates(self, dimension_scores: dict) -> dict:
    """Evaluate quality gates based on dimension scores"""
    
    quality_gates = {}
    
    for dimension, score in dimension_scores.items():
        threshold = self.quality_thresholds.get(dimension, 0.8)
        
        if score >= threshold:
            quality_gates[f"{dimension}_quality"] = "pass"
        elif score >= threshold * 0.8:  # 80% of threshold
            quality_gates[f"{dimension}_quality"] = "pass_with_warnings"
        else:
            quality_gates[f"{dimension}_quality"] = "fail"
    
    # Overall quality gate
    overall_threshold = self.quality_thresholds.get("overall_quality", 0.85)
    overall_score = sum(dimension_scores.values()) / len(dimension_scores)
    
    if overall_score >= overall_threshold:
        quality_gates["overall_quality"] = "pass"
    elif overall_score >= overall_threshold * 0.8:
        quality_gates["overall_quality"] = "pass_with_warnings"
    else:
        quality_gates["overall_quality"] = "fail"
    
    return quality_gates

def validate_output(self, output: dict) -> bool:
    """Validate validation results output"""
    validation_results = output.get("validation_results", {})
    
    required_fields = [
        "overall_score", "dimension_scores", "issues_found", 
        "quality_gates", "compliance_check"
    ]
    
    if not all(field in validation_results for field in required_fields):
        return False
    
    # Check score ranges
    overall_score = validation_results.get("overall_score", 0)
    if not 0 <= overall_score <= 1:
        return False
    
    dimension_scores = validation_results.get("dimension_scores", {})
    for score in dimension_scores.values():
        if not 0 <= score <= 1:
            return False
    
    return True
```

**📤 OUTPUT:** Complete validation process với comprehensive quality assessment
**⏱️ Duration:** 5 giờ

---

## 📊 **TỔNG KẾT AGENT 5**

### **Tổng thời gian ước tính:** 50 giờ (6-7 ngày làm việc)

### **Deliverables chính:**
1. **ValidationAgent class** - Complete implementation
2. **Validation rules engine** - Configurable rules cho multiple dimensions
3. **Completeness validation** - Structure, content, timeline, metadata validation
4. **Consistency validation** - Naming, format, data, cross-reference consistency
5. **Feasibility assessment** - Timeline, resource, technical, dependency feasibility
6. **Auto-fix capabilities** - Intelligent automatic issue resolution
7. **Quality gates** - Pass/fail criteria với compliance checking

### **Input → Output Transformation:**
```
Complete Plan Data (từ tất cả agents trước)
    ↓
{
    "validation_results": {
        "overall_score": 0.94,
        "dimension_scores": {
            "completeness_score": 0.96,
            "consistency_score": 0.91,
            "feasibility_score": 0.87,
            "requirement_alignment": 0.98
        },
        "issues_found": [
            {
                "issue_id": "TIMELINE_001",
                "type": "timeline_too_aggressive",
                "severity": "medium",
                "description": "8 weeks may be optimistic for intermediate complexity",
                "suggested_fix": "Consider extending to 10-12 weeks",
                "auto_fixable": true
            }
        ],
        "auto_fixes_applied": [
            {
                "issue_id": "CONTENT_002",
                "fix_applied": "Added acceptance criteria to 3 tasks",
                "location": "milestone_M2.tasks"
            }
        ],
        "improvements_suggested": [
            {
                "area": "resource_allocation",
                "suggestion": "Add QA resource from week 6 instead of week 9",
                "impact": "medium",
                "expected_benefit": "Earlier bug detection"
            }
        ],
        "quality_gates": {
            "completeness_quality": "pass",
            "consistency_quality": "pass",
            "feasibility_quality": "pass_with_warnings",
            "overall_quality": "pass"
        }
    }
}
```

**Agent 5 validates complete plan quality và provides actionable improvement suggestions!** ✅
